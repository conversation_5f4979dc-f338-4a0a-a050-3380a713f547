<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Consumer;
use App\Admin\Extensions\ExpenseExporter;
use App\Admin\Extensions\Express;
use App\Admin\Extensions\Express\EmsExpress;
use App\Admin\Extensions\ExpressYzExporter;
use App\Admin\Extensions\PostRepairAmountExporter;
use App\Admin\Extensions\PostRepairRegionExporter;
use App\Admin\Extensions\Producer;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\ExpGoOneOrder;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Admin\Extensions\Tools\YzExpGoOneOrder;
use App\Models\ChinaArea;
use App\Models\Endpoint;
use App\Models\Machine;
use App\Models\Order;

use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\PostRepairExpense;
use App\User;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use Barryvdh\Debugbar\Facade as DebugBar;

class RepairExpGoController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('回寄产品');
            $content->description('本页面不显示也不统计“代理商寄修”的订单');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('回寄产品');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('回寄产品--查看');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('post_repair_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'post_repair_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $check_man = DB::table('admin_users')
                ->join('order', 'order.check_man', '=', 'admin_users.id')
                ->where('order.check_man', '=', $order['check_man'])->pluck('admin_users.name')->toArray();
            $check_man = implode('', $check_man);
            $repair_man = DB::table('admin_users')
                ->join('order', 'order.repair_man', '=', 'admin_users.id')
                ->where('order.repair_man', '=', $order['repair_man'])->pluck('admin_users.name')->toArray();
            $repair_man = implode('', $repair_man);
            $content->body(view('admin/repair_order/view', compact('order', 'malfunction', 'accessory', 'check_man', 'repair_man')));

        });
    }

    public function express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            //前端好像没办法阻止form自动提交事件，这里多加一个参数防止重复生成
            if (!isset($data['is_ajax']) || $data['is_ajax'] != 1) {
                return 1;
            }
            $data['custid'] = env('SF_CUSTID');
            $data['template'] = '思为滕-下单';
            $data['express_type'] = 103;
            $data['pay_method'] = 1;
            $data['type'] = 2;
            $id = $data['id'];
            unset($data['id']);
//            dd($data);
            $express = new Express();
            $result = $express->create_express_order($data);
//            $result = array();
//            $result['Head'] = 'a';
//            $result['ERROR'] = '1111';

            if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                    'status' => 1,
                    'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
//                dd($order);
                $order->status = Order::EXP_GO_SUCCESS;
                $order->rb_go_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                $order->go_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                $order->go_exp_com = '顺丰快递';
                $order->go_sure = 1;
                $order->updated_at_last = date('Y-m-d H:i:s');
                $order->save();
                $order_extend = OrderExtend::where(['sn' => $order['sn']])->first();
                if ($order_extend->exchange_number) {
                    $producer = new Producer();
                    $category_id = Machine::where(['model_id' => $order['model_id']])->value('category_id');
                    $content_data = array([
                        'category_id' => $category_id,
                        'sn' => $order->sn,
                        'model_id' => $order->model_id,
                        'model_name' => $order->model_name,
                        'serial' => $order->serial,
                        'barcode' => $order->barcode,
                        'imei' => $order->imei,
                        'status' => $order->status,
                        'updated_at_last' => $order->updated_at_last,
                        'exchange_number' => $order_extend->exchange_number
                    ]);
//                    dd($content_data);
                    $content_data = json_encode($content_data);
                    $producer->warranty_return_or_exchange_producer($content_data);

                }

                return response()->json(['success' => true, 'id' => $id,'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno']]);
//                return redirect('/admin/repair_exp_go');
            } else if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] == 3) {
//                dd($result);
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => "地区无法到达",
//                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            } else if ($result['Head'] == 'ERR') {
                $error = new MessageBag([
                    'title' => '错误提示',
//                    'message' => "内部错误",
                    'message' => json_encode($result['ERROR']),
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => "内部错误",
//                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $order = Order::where(['id' => $id])->first();
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-快递下单');
        // 使用自定义模板替代 Laravel Admin 表单
        $content->body(view('admin.express.express', compact('id', 'order', 'endpoint', 'province', 'city', 'district')));
        return $content;
    }

    public function express_one_order(Content $content)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['custid'] = env('SF_CUSTID');
            $data['template'] = '思为滕-下单';
            $data['express_type'] = 103;
            $data['pay_method'] = 1;
            $data['type'] = 2;
            $ids = explode(',', $data['ids']);
            unset($data['ids']);
            $express = new Express();
            $result = $express->create_express_order($data);
            $ret = false;
            foreach ($ids as $id) {
                if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                    //更新快递信息
                    PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                        'status' => 1,
                        'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                    ]);
                    //写入订单信息
                    $order = Order::where(['id' => $id])->first();
//                dd($order);
                    $order->status = Order::EXP_GO_SUCCESS;
                    $order->rb_go_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                    $order->go_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                    $order->go_exp_com = '顺丰快递';
                    $order->go_sure = 1;
                    $order->updated_at_last = date('Y-m-d H:i:s');
                    $order->save();
                    $ret = true;
                } else {
                    Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                }
            }
            if ($ret) {
                return redirect('/admin/repair_exp_go');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['ERROR'],
                ]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $ids = Request::get('ids');
        $sns = array();
        $orders = Order::whereIn('id', $ids)->get();
        //比对收货地址
        $cmp_order = $orders[0];
        foreach ($orders as $order) {
            if ($order->name != $cmp_order->name
                || $order->phone != $cmp_order->phone
                || $order->uid != $cmp_order->uid
                || $order->province != $cmp_order->province
                || $order->city != $cmp_order->city
                || $order->district != $cmp_order->district
                || $order->address != $cmp_order->address) {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => '快递订单信息不相同',
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $sns[] = $order->sn;
        }
        $order = $cmp_order;
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-多订单回寄同一个快递');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_exp_go/express_one_order');
        $form->hidden('ids')->default(implode(',', $ids));
        $form->hidden('sn')->default(implode(',', $sns));
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function yz_express(Content $content, $id = null)
    {
        DebugBar::info(['yz_express' => date('Y-m-d H:i:s')]);
        if (request()->isMethod('post')) {
            $data = request()->all();
            //验证参数
            $valid_pass = true;
            $error_message = '';
            if (strlen($data['j_tel']) > 20 || strlen($data['d_tel']) > 20) {
                $valid_pass = false;
                $error_message .= "电话长度超过20个字符\t";
            }
            $pattern = '/[^\x00-\x80]/';
            if (preg_match($pattern, $data['j_tel']) || preg_match($pattern, $data['d_tel'])) {
                $valid_pass = false;
                $error_message .= "电话包含中文\t";
            }
            if (!$valid_pass) {
                $error = new MessageBag([
                    'title' => '格式验证不通过',
                    'message' => $error_message,
                ]);
                return back()->withInput()->with(compact('error'));
            }

            $data['pay_method'] = 1;  // 寄付
            $data['type'] = 2;  // 寄走
            $id = $data['id'];
            unset($data['id']);

            $express = new EmsExpress();
            $result = $express->create_order($data);
            DebugBar::info(['data' => $data, 'create_order result' => $result]);
            if ($result['ok'] == 1) {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['readboy_sn']])->update([
                    'status' => 1,
                    'exp_sn' => $result['exp_sn'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
                $order->status = Order::EXP_GO_SUCCESS;
                $order->rb_go_exp_sn = $result['readboy_sn'];
                $order->go_exp_sn = $result['exp_sn'];
                $order->go_exp_com = '邮政快递';
                $order->go_sure = 1;
                $order->updated_at_last = date('Y-m-d H:i:s');
                $order->save();
                $order_extend = OrderExtend::where(['sn' => $order['sn']])->first();
                if ($order_extend->exchange_number) {
                    $producer = new Producer();
                    $category_id = Machine::where(['model_id' => $order['model_id']])->value('category_id');
                    $content_data = array([
                        'category_id' => $category_id,
                        'sn' => $order->sn,
                        'model_id' => $order->model_id,
                        'model_name' => $order->model_name,
                        'serial' => $order->serial,
                        'barcode' => $order->barcode,
                        'imei' => $order->imei,
                        'status' => $order->status,
                        'updated_at_last' => $order->updated_at_last,
                        'exchange_number' => $order_extend->exchange_number
                    ]);
//                    dd($content_data);
                    $content_data = json_encode($content_data);
                    $producer->warranty_return_or_exchange_producer($content_data);

                }
                return redirect('/admin/repair_exp_go');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => json_encode($result, JSON_UNESCAPED_UNICODE),
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $order = Order::where(['id' => $id])->first();
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-邮政快递下单');
        $content->body('两个电话字段：长度不能超过20个字符，不能包含中文');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_exp_go/yz_express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function yz_express_one_order(Content $content)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['pay_method'] = 1;
            $data['type'] = 2;
            $ids = explode(',', $data['ids']);
            unset($data['ids']);
            $express = new EmsExpress();
            $result = $express->create_order($data);
            $ret = false;

            foreach ($ids as $id) {
                if ($result && $result['ok'] == 1) {
                    //更新快递信息
                    PostExpress::where(['readboy_sn' => $result['readboy_sn']])->update([
                        'status' => 1,
                        'exp_sn' => $result['exp_sn'],
                    ]);
                    //写入订单信息
                    $order = Order::where(['id' => $id])->first();
//                dd($order);
                    $order->status = Order::EXP_GO_SUCCESS;
                    $order->rb_go_exp_sn = $result['readboy_sn'];
                    $order->go_exp_sn = $result['exp_sn'];
                    $order->go_exp_com = '邮政快递';
                    $order->go_sure = 1;
                    $order->updated_at_last = date('Y-m-d H:i:s');
                    $order->save();
                    $ret = true;
                } else {
                    Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                }
            }
            if ($ret) {
                return redirect('/admin/repair_exp_go');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['msg'],
                ]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $ids = Request::get('ids');
        $sns = array();
        $orders = Order::whereIn('id', $ids)->get();
        //比对收货地址
        $cmp_order = $orders[0];
        foreach ($orders as $order) {
            if ($order->name != $cmp_order->name
                || $order->phone != $cmp_order->phone
                || $order->uid != $cmp_order->uid
                || $order->province != $cmp_order->province
                || $order->city != $cmp_order->city
                || $order->district != $cmp_order->district
                || $order->address != $cmp_order->address) {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => '快递订单信息不相同',
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $sns[] = $order->sn;
        }
        $order = $cmp_order;
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-多订单回寄同一个快递');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_exp_go/yz_express_one_order');
        $form->hidden('ids')->default(implode(',', $ids));
        $form->hidden('sn')->default(implode(',', $sns));
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function manual_express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            DebugBar::info(['manual_express' => date('Y-m-d H:i:s'), 'params' => $data]);
            if (!array_key_exists('id', $data) || !array_key_exists('sn', $data) ||
                !array_key_exists('exp_com', $data) || !array_key_exists('exp_sn', $data) ||
                !array_key_exists('pay_method', $data)) {
                $error = new MessageBag([
                    "title" => '错误提示',
                    "message" => '参数错误'
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $sn = $data['sn'];
            if ($sn == null || $sn == '') {
                $error = new MessageBag([
                    "title" => '错误提示',
                    "message" => '参数错误, 缺少订单号'
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $exp_com = $data['exp_com'];
            $exp_sn = $data['exp_sn'];
            $pay_method = $data['pay_method'];
            if ($exp_com == null || $exp_com == '' || $exp_sn == null || $exp_sn == '' ||
                $pay_method == null || $pay_method == '') {
                $error = new MessageBag([
                    "title" => '错误提示',
                    "message" => '参数错误, 快递信息必填'
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $pay_method = array_key_exists($pay_method, PostExpress::PAY_METHOD) ? intval($pay_method) : 0;
            $order = Order::where(['sn' => $sn])->first();
            if (!in_array($order->status, self::ORDER_EXP_GO_STATUS_BEFORE)) {
                $error = new MessageBag([
                    "title" => '错误提示',
                    "message" => '订单 ' . $sn . ' 状态不可回寄'
                ]);
                return back()->withInput()->with(compact('error'));
            }

            $rb_exp_sn = Express\BaseExpress::create_exp_sn($sn);
            $exp_data = [
                'sn' => $sn,
                'j_contact' => '',
                'j_tel' => '',
                'j_province' => '',
                'j_city' => '',
                'j_county' => '',
                'j_address' => '',
                'd_contact' => '',
                'd_tel' => '',
                'd_province' => '',
                'd_city' => '',
                'd_county' => '',
                'd_address' => '',
                '_token' => $data['_token'],
                'pay_method' => $pay_method,
                'type' => 2,
                'orderid' => $rb_exp_sn,
                'exp_sn' => $exp_sn,
            ];
            DebugBar::info(compact('pay_method', 'rb_exp_sn', 'exp_data'));

            $express = new PostExpress();
            $express->pr_sn = $sn;
            $express->readboy_sn = $rb_exp_sn;
            $express->com = array_key_exists($exp_com, PostExpress::EXPRESS_COM) ? PostExpress::EXPRESS_COM[$exp_com] : '';
            $express->com_type = $exp_com;
            $express->exp_sn = $exp_sn;
            $express->status = 1;
            $express->type = 2;
            $express->pay_method = $pay_method;
            $express->data = $exp_data;
            $express->save();

            //写入订单信息
            $order->status = Order::EXP_GO_SUCCESS;
            $order->rb_go_exp_sn = $rb_exp_sn;
            $order->go_exp_sn = $exp_sn;
            $order->go_exp_com = array_key_exists($exp_com, PostExpress::GO_EXP_COM) ? PostExpress::GO_EXP_COM[$exp_com] : '';
            $order->go_sure = 1;
            $order->updated_at_last = date('Y-m-d H:i:s');
            $order->save();
            $order_extend = OrderExtend::where(['sn' => $order['sn']])->first();
            if ($order_extend->exchange_number) {
                $producer = new Producer();
                $category_id = Machine::where(['model_id' => $order['model_id']])->value('category_id');
                $content_data = array([
                    'category_id' => $category_id,
                    'sn' => $order->sn,
                    'model_id' => $order->model_id,
                    'model_name' => $order->model_name,
                    'serial' => $order->serial,
                    'barcode' => $order->barcode,
                    'imei' => $order->imei,
                    'status' => $order->status,
                    'updated_at_last' => $order->updated_at_last,
                    'exchange_number' => $order_extend->exchange_number
                ]);
                $content_data = json_encode($content_data);
                $producer->warranty_return_or_exchange_producer($content_data);
            }
            return redirect('/admin/repair_exp_go');
        }

        $content->header('回寄-手工标回寄');
        $order = Order::where(['id' => $id])->first();
        if (!$order || !isset($order->id) || !isset($order->sn) || !isset($order->status)) {
            $content->body('订单不存在');
            return $content;
        }
        $sn = $order->sn;
        if (!in_array($order->status, self::ORDER_EXP_GO_STATUS_BEFORE)) {
            $content->body('订单 ' . $sn . ' 状态不可回寄');
            return $content;
        }

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_exp_go/manual_express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->display('sn', '寄修单号')->default($order->sn);
        $form->select('pay_method', '支付方式')->options(PostExpress::PAY_METHOD);
        $form->select('exp_com', '快递公司')->options(PostExpress::EXPRESS_COM);
        $form->text('exp_sn', '快递单号')->rules('required');

        $content->body($form);
        return $content;
    }

    private const ORDER_EXP_GO_STATUS_BEFORE = [Order::REPAIR_FINISH, Order::REPAIR_REFUSE, Order::EXP_GO_FAIL];

    private const ORDER_CANCEL_EXP_STATUS_BEFORE = [Order::EXP_GO_SUCCESS, Order::ORDER_FINISH];

    public function cancel_express()
    {
        DebugBar::info(['cancel_express' => date('Y-m-d H:i:s')]);
        $sn = Request::get('sn');
        if ($sn) {
            $o = Order::where(['sn' => $sn])->first();
            DebugBar::info(compact('o'));
            if (in_array($o->status, self::ORDER_CANCEL_EXP_STATUS_BEFORE)) { //检查订单状态
                //从日志读取最后状态值
                $ol = OrderLog::where(['pr_sn' => $sn])->whereNotIn('pr_status', self::ORDER_CANCEL_EXP_STATUS_BEFORE)
                    ->orderBy('id', 'desc')->first();
                DebugBar::info(compact('ol'));
                //设置订单状态,可否不自动修改日志?
                //$o->no_log = true;
                $o->status = Order::STATUS[$ol->pr_status] ? $ol->pr_status : Order::EXP_GO_FAIL;
                $o->go_sure = 0;
                $o->save();
                //更新快递信息
                $pe = PostExpress::where(['readboy_sn' => $o->rb_go_exp_sn, 'type' => 2])
                    ->orderBy('id', 'desc')->first();
                DebugBar::info(compact('pe'));
                if ($pe && $pe->status == 1) {
                    $pe->status = 0;
                    $pe->save();
                }
                return array('status' => 1, 'info' => "订单 $sn 取消回寄成功，状态设置为“" . Order::STATUS[$o->status] . '”');
            } else {
                return array('status' => 0, 'info' => "订单 $sn 不处于可取消回寄的状态");
            }
        } else {
            return array('status' => 0, 'info' => '参数错误');
        }
    }

    public function cancel(Request $request)
    {
        foreach (Order::find($request->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    public function expense(Request $request)
    {
        $sn = Request::get('sn');
        $save['pr_sn'] = $sn;
        $save['updated_at'] = date('Y-m-d H:i:s');
        PostRepairExpense::updateOrInsert(array('pr_sn' => $sn), $save);
    }

    public function expense_print()
    {
        $sn = Request::get('sn');
        $order = Order::where(['sn' => $sn])->first();
        $post_malfunction = DB::table('pr_malfunction')
            ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
        $malfunction = implode('，', $post_malfunction);
        $post_accessory = DB::table('pr_accessory')
            ->join('machine_accessory_relation', 'machine_accessory_relation.id', '=', 'pr_accessory.mar_id')
            ->join('machine_accessory', 'machine_accessory.id', '=', 'machine_accessory_relation.accessory_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
        $accessory = implode('，', $post_accessory);

        $pr_material = DB::table('pr_used_material')
            ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
            ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
            ->where('pr_used_material.pr_sn', $order['sn'])
            ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                'material.code as code', 'material.old_code as old_code', 'material.specification as specification', 'material.from as from')
            ->get()
            ->toArray();
//            dd($pr_material);
        return view('admin/repair_exp_go/expense_print', compact('order', 'malfunction', 'accessory', 'pr_material'));
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $action_style = <<<css
    .action_link {
        margin-right: 1mm;
    }
    .action_link:hover {
        text-decoration-line: underline;
    }
css;
        $action_style_str = base64_encode($action_style);

        $script_style = <<<js

    // 设置操作链接-设置样式
    const action_style = decodeURIComponent(escape(atob('$action_style_str')));
    $('head').append('<style type="text/css">' + action_style + '</style>');
js;
        Admin::script($script_style);

        $script = <<<js
            //条码
            $('input[name="sn"]').bind('mouseover', function(){this.select();});
            $('input[name="sn"]').bind('click', function(){this.select();});
            $('input[name="sn"]').click();
            //取消回寄
            $('.cancel_express').on('click', function () {
                var sn = $(this).attr('value');
                var msg = "是否要将订单状态设为回寄之前的状态？";
                var res = confirm(msg);
                if (res == true) {
                    $.ajax({
                        method: 'get',
                        url: '/admin/repair_exp_go/cancel_express',
                        data: { sn: sn },
                        success: function (res) {
                            layer.msg(res.info)
                            if(res.status == 1){
                                toastr.success(res.info);
                            } else {
                                toastr.error(res.info);
                            }
                        }
                    })
                }
            })
            //打印物料损耗报告
            $('.print_expense').click(function(){
                var id = $(this).attr('value');
                $.get('/admin/repair_exp_go/expense?sn='+id, function(result){
                    layer.open({
                        type: 2,
                        title: '打印损耗报告',
                        shadeClose: true,
                        shade: 0.8,
                        area: ['600px', '90%'],
                        content: 'repair_exp_go/expense_print?sn='+id //iframe的url
                    });
                });
            });
            //打印检测报告
            $('.print_check').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '检测报告（双击:切换配件列表显示；单击图片或按P键:启动打印）',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['600px', '90%'],
                      content: 'repair_check/print/'+id //iframe的url
                });
            });
            //打印快递条
            $('.print').click(function(){
                var id = $(this).attr('value');
//                LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
//                LODOP.SET_PRINT_PAGESIZE(2,'55mm','80mm', '');//设定纸张方向和尺寸
//                LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
//                LODOP.SET_PRINT_STYLE("FontSize",20);
//                LODOP.SET_PRINT_STYLE("Bold",1);
//                LODOP.ADD_PRINT_BARCODE('15mm','15mm','54mm','13mm',"128B",id);
//                LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
//                LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
////                LODOP.ADD_PRINT_TEXT('30mm','10mm','40mm','10mm','顺丰单号：'+id);
//                LODOP.ADD_PRINT_TEXT('30mm','10mm','80mm','20mm',id);
////                        LODOP.SET_PRINT_STYLEA(4,"TextFrame",2);
////                LODOP.PREVIEW();

                //横向打印
                LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
                LODOP.SET_PRINT_PAGESIZE(1,'80mm','40mm', '');//设定纸张方向和尺寸
                LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
                LODOP.SET_PRINT_STYLE("FontSize",20);
                LODOP.SET_PRINT_STYLE("Bold",1);
                LODOP.ADD_PRINT_BARCODE('5mm','15mm','54mm','13mm',"128B",id);
                LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
                LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
//                LODOP.ADD_PRINT_TEXT('20mm','20mm','40mm','10mm','顺丰单号：'+id);
                LODOP.ADD_PRINT_TEXT('25mm','10mm','80mm','20mm',id);
//                LODOP.ADD_PRINT_SHAPE(4,'35mm','0mm','80mm','5mm',0,1,"#000000");
//                        LODOP.SET_PRINT_STYLEA(4,"TextFrame",2);
//                LODOP.PREVIEW();
//                LODOP.PRINT_DESIGN();

                LODOP.PRINT();
            });
            $('.btn-order-export').click(function(){
                 url = window.location.href;
                 if (url.indexOf("&order_export=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&order_export=1&_export_=1";
                    } else {
                        url = url + "&order_export=1&_export_=1";
                    }
                }
                window.open(url);
            });
            $('.btn-order-amount-export').click(function(){
                 url = window.location.href;
                 if (url.indexOf("&order_amount_export=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&order_amount_export=1&_export_=1";
                    } else {
                        url = url + "&order_amount_export=1&_export_=1";
                    }
                }
                window.open(url);
            });
            $('.btn-express-yz-export').click(function(){
                 url = window.location.href;
                 if (url.indexOf("&express_yz_export=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&express_yz_export=1&_export_=1";
                    } else {
                        url = url + "&express_yz_export=1&_export_=1";
                    }
                }
                window.open(url);
            });
js;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => function ($query) {
                        $query->orwhere([['status', '=', Order::REPAIR_FINISH], ['quality', '=', 1], ['order.type', '!=', '2']])
                            ->orwhere('status', '=', Order::REPAIR_REFUSE)
                            ->orwhere([['go_sure', '=', 1], ['status', '=', Order::EXP_GO_SUCCESS], ['order.type', '!=', '2']])
                            ->orwhere([['status', '=', Order::EXP_GO_FAIL], ['order.type', '!=', '2']])
                            ->orwhere([['status', '=', Order::ORDER_FINISH], ['order.type', '!=', '2']]);
                    },
                ],
                1 => [
                    'name' => '未回寄',
                    'param' => function ($query) {
                        $query->orwhere([['quality', '=', 1], ['status', '=', Order::REPAIR_FINISH], ['order.type', '!=', '2']])
                            ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '!=', '2']]);
                    },
                ],
                2 => [
                    'name' => '回寄成功',
                    'param' => [['go_sure', '=', 1], ['status', '=', Order::EXP_GO_SUCCESS], ['order.type', '!=', '2']],
                ],
                3 => [
                    'name' => '回寄失败',
                    'param' => [['status', '=', Order::EXP_GO_FAIL], ['order.type', '!=', '2']],
                ],
                4 => [
                    'name' => '已完成',
                    'param' => [['status', '=', Order::ORDER_FINISH], ['order.type', '!=', '2']],
                ],
            ];
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('updated_at', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('updated_at', 'desc');
            //同源快递筛选
            if (Request::get('check_repeat_exp')) {
                $check_id = Request::get('check_repeat_exp');
                $order = Order::where('id', $check_id)->first();
                $where = array();
                $where[] = array('uid', $order->uid);
                $where[] = array('name', $order->name);
                $where[] = array('phone', $order->phone);
                $where[] = array('province', $order->province);
                $where[] = array('city', $order->city);
                $where[] = array('district', $order->district);
                $where[] = array('address', $order->address);
                $grid->model()->where($where);
            }

            $grid->disableCreation();
//            $grid->disableExport();
            $updated_at_last = Request::get('updated_at_last');
//            dd($updated_at_last);

            $grid->exporter(new ExpenseExporter());
            if (Request::get('order_export')) {
                $grid->exporter(new PostRepairRegionExporter());
            }
            if (Request::get('order_amount_export')) {
                $grid->exporter(new PostRepairAmountExporter());
            }
            if (Request::get('express_yz_export')) {
                $grid->exporter(new ExpressYzExporter());
            }

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->equal('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                $a = User::join('order as o', 'o.repair_man', '=', 'admin_users.id')->pluck('admin_users.name', 'admin_users.id')->all();
                $filter->equal('repair_man', '维修人')->select(
                    $a
                );

                $filter->equal('repeat_order', '寄修状态')
                    ->select(Order::repeat_repair);

                $filter->where(function ($query) {
                    $query->where('go_exp_com', 'like', $this->input . '%');
                }, '寄去快递公司')->select(['顺丰' => '顺丰', '圆通' => '圆通', '邮政' => '邮政']);
                $filter->between('updated_at', '最后操作时间')->datetime();
                $filter->between('updated_at_last', '下单时间')->datetime();
                $filter->between('created_at', '订单提交日期')->datetime();
                //添加物料筛选
                $filter->where(function ($query) {
                    $material_code = $this->input;
                    if(empty($material_code)) return;

                    // 先获取匹配的物料ID
                    $material_ids = DB::table('material')
                        ->where('code', '=', $material_code)
                        ->pluck('id');

                    if($material_ids->isEmpty()) {
                        $query->whereRaw('1=0'); // 如果没有匹配的物料,直接返回空结果
                        return;
                    }

                    // 使用IN查询替代EXISTS
                    $query->whereIn('order.sn', function($q) use ($material_ids) {
                        $q->select('o.sn')
                            ->from('pr_material AS pr_m')
                            ->join('order AS o', 'pr_m.pr_sn', '=', 'o.sn')
                            ->leftJoin('pr_expense AS pr_e', 'o.sn', '=', 'pr_e.pr_sn')
                            ->leftJoin('material AS m', 'pr_m.material_id', '=', 'm.id')
                            ->whereIn('pr_m.material_id', $material_ids)
                            ->where('o.status', '>=', 500)
                            ->whereNull('pr_e.updated_at')
                            ->whereNotIn('o.connect', [3, 6]);
                    });
                }, '物料代码占用查询');
            });

            //工具条
            $grid->tools(function ($tools) use ($option) {
                //自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));

                $html = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <a href ="" class="btn btn-sm btn-success btn-order-export" >
                        <i class="fa fa-print" ></i > 导出寄修概况
                      </a >
                </div >
EOF;
                $tools->append($html);

                $html_print = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <a href ="" class="btn btn-sm btn-success btn-order-amount-export" >
                        <i class="fa fa-print" ></i > 导出寄修费用概况
                      </a >
                </div >
EOF;
                $tools->append($html_print);

                $html_yz = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <a href ="" class="btn btn-sm btn-success btn-express-yz-export" >
                        <i class="fa fa-print" ></i > 导出邮政订单
                      </a >
                </div >
EOF;
                $tools->append($html_yz);

                $tools->batch(function ($batch) {
                    $batch->add('顺丰快递同一订单', new ExpGoOneOrder());
                    $batch->add('邮政快递同一订单', new YzExpGoOneOrder()); // 邮政终止,不注释会出错. 又开回ems了
                });
            });

            //表格显示列
            $grid->id('ID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('维修订单号');
            //$grid->barcode('S/N码');
            //$grid->model_name('机型');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->name('联系人');
            //$grid->phone('用户联系方式');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            $grid->column('地址')->display(function () {
                return '<span style="padding-right: 1ch">' . $this->province .
                    $this->city . '</span>' .
                    $this->address;
            });
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->pay_amount('支付费用')->display(function ($amount) {
                return '￥' . $amount;
            });
            $grid->repair_user()->name('维修人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            //$grid->go_exp_sn('快递单号');
            $grid->column('快递单号')->display(function () {
                return $this->go_exp_sn . '<br/>' . $this->go_exp_com;
            });
            $grid->expense()->updated_at('核销时间');
            $grid->updated_at('最后操作时间');
            $grid->updated_at_last('下单时间')->sortable();

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '" class="action_link"><span style="color:blue">查看</span></a>';
                $actions->append($html);
                if ($status == Order::REPAIR_FINISH || $status == Order::REPAIR_REFUSE || $status == Order::EXP_GO_FAIL) {
                    $c = 'repair_exp_go/express/' . $actions->getKey();
                    $html = '<a href="' . $c . '" class="action_link"><span style="color:orange">顺丰下单</span></a>';
                    $actions->append($html);
                    $c = 'repair_exp_go/yz_express/' . $actions->getKey();
                    $html = '<a href="' . $c . '" class="action_link"><span style="color:orange">邮政下单</span></a>';
                    $actions->append($html);
                    $c = 'repair_exp_go/manual_express/' . $actions->getKey();
                    $html = '<a href="' . $c . '" class="action_link"><span style="color:black">手工标回寄</span></a>';
                    $actions->append($html);
                }
                if ($status == Order::REPAIR_FINISH || $status == Order::REPAIR_REFUSE) {
                    $c = 'repair_exp_go?check_repeat_exp=' . $actions->getKey();
                    $html = '<a href="' . $c . '" class="action_link"><span style="color:orange">快递筛选</span></a>';
                    $actions->append($html);
                }
                if (in_array($status, self::ORDER_CANCEL_EXP_STATUS_BEFORE)) {
                    $v = $actions->row->sn;
                    $html = '<a href="javascript:void(0);" class="action_link cancel_express" value="' . $v . '"><span style="color:black">取消回寄</span></a>';
                    $actions->append($html);
                }
                if ($status == Order::EXP_GO_SUCCESS || $status == Order::ORDER_FINISH || $status == Order::REPAIR_FINISH || $status == Order::EXP_GO_FAIL) {
                    $v = $actions->row->id;
                    $html = '<a href="javascript:void(0);" class="action_link print_check" value="' . $v . '"><span style="color:blue">【打印检测报告】</span></a>';
                    $actions->append($html);
                    $expense = PostRepairExpense::where('pr_sn', $actions->row->sn)->first();
                    if ($expense) {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="action_link print_expense" value="' . $v . '"><span style="color:green">【打印物料损耗报告】</span></a>';
                        $actions->append($html);
                    } else {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="action_link print_expense" value="' . $v . '"><span style="color:red">【打印物料损耗报告】</span></a>';
                        $actions->append($html);
                    }
                    $exp_sn = $actions->row->go_exp_sn;
                    $html = '<a href="javascript:void(0)" value="' . $exp_sn . '" class="action_link print"><span style="color:orange">打印快递条</span></a>';
                    $actions->append($html);
                }
                if ($status == Order::ORDER_FINISH) {
                    $expense = PostRepairExpense::where('pr_sn', $actions->row->sn)->first();
                    if ($expense) {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="action_link print_expense" value="' . $v . '"><span style="color:green">【打印物料损耗报告】</span></a>';
                        $actions->append($html);
                    } else {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="action_link print_expense" value="' . $v . '"><span style="color:red">【打印物料损耗报告】</span></a>';
                        $actions->append($html);
                    }
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(Order::class, function (Form $form) {

            $form->select('repair_status', '维修状态')
                ->options(Order::repair_status);
            $form->hidden('repair_man');
            $form->hidden('status');
            $form->saving(function (Form $form) {
                $form->repair_man = Admin::user()->id;
                if ($form->repair_status == 1)
                    $form->status = Order::REPAIR_FINISH;
            });
        });
    }
}